# API 接口文档

## 好友系统相关 API

### 1. 获取用户基本信息

- **接口路径**: `GET /api/users`
- **功能**: 根据 userId 获取用户基本信息，用于添加好友时的用户搜索
- **请求参数**:

  ```typescript
  // Query 参数
  {
    userId: string; // 要查询的用户ID
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      userId: string;
      nickname: string;
      avatar: string | null;
    }
  }

  // 错误响应
  {
    error: string; // 错误信息
  }
  ```

- **状态码**:

  - `200`: 成功
  - `400`: 缺少参数
  - `404`: 用户不存在
  - `500`: 服务器错误

### 2. 获取好友列表

- **接口路径**: `GET /api/friends`
- **功能**: 获取用户的所有好友关系，包括已接受、待处理和已拒绝的请求
- **请求参数**:
  ```typescript
  // Query 参数
  {
    userId: string; // 当前用户ID
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      accepted: Array<{
        friendshipId: string;
        friend: {
          userId: string;
          nickname: string;
          avatar: string | null;
        };
        status: "accepted";
        createdAt: Date;
        updatedAt: Date;
        type: "sent" | "received"; // 请求方向
      }>;
      pending: Array<{
        friendshipId: string;
        friend: {
          userId: string;
          nickname: string;
          avatar: string | null;
        };
        status: "pending";
        createdAt: Date;
        updatedAt: Date;
        type: "sent" | "received";
      }>;
      rejected: Array<{
        friendshipId: string;
        friend: {
          userId: string;
          nickname: string;
          avatar: string | null;
        };
        status: "rejected";
        createdAt: Date;
        updatedAt: Date;
        type: "sent" | "received";
      }>;
    }
  }
  ```

### 3. 发送好友请求

- **接口路径**: `POST /api/friends`
- **功能**: 向指定用户发送好友请求
- **测试性 Curl：**

```bash
curl -i -X post "http://*************:3000/api/friends" \
  -H "content-type: application/json" \
  -d '{"userId":"alice","friendId":"bob"}'

```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    userId: string; // 发起请求的用户ID
    friendId: string; // 目标用户ID
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "好友请求发送成功";
    data: {
      id: string;
      userId: string;
      friendId: string;
      status: "pending";
      createdAt: Date;
      updatedAt: Date;
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 参数错误或关系已存在
  - `404`: 目标用户不存在
  - `500`: 服务器错误

### 4. 处理好友请求

- **接口路径**: `PUT /api/friends`
- **功能**: 同意或拒绝收到的好友请求
- **请求参数**:
  ```typescript
  // Body 参数
  {
    userId: string; // 当前用户ID（处理请求的用户id）
    friendId: string; // 发起请求的用户ID
    action: "accept" | "reject"; // 操作类型
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "已同意好友请求" | "已拒绝好友请求";
    data: {
      id: string;
      userId: string;
      friendId: string;
      status: "accepted" | "rejected";
      createdAt: Date;
      updatedAt: Date;
    }
  }
  ```

### 5. 删除好友关系

- **接口路径**: `DELETE /api/friends`
- **功能**: 删除已建立的好友关系
- **请求参数**:
  ```typescript
  // Body 参数
  {
    userId: string; // 当前用户ID
    friendId: string; // 要删除的好友ID
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "好友关系已删除";
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 参数错误
  - `404`: 好友关系不存在
  - `500`: 服务器错误

## 使用说明

### 好友系统工作流程

1. **搜索用户**: 使用 `GET /api/users?userId=xxx` 获取用户信息
2. **发送好友请求**: 使用 `POST /api/friends` 发送请求
3. **查看好友列表**: 使用 `GET /api/friends?userId=xxx` 查看所有关系
4. **处理请求**: 使用 `PUT /api/friends` 同意或拒绝请求
5. **删除好友**: 使用 `DELETE /api/friends` 删除关系

### 注意事项

- 所有接口都需要提供有效的 userId
- 好友关系是单向存储的，但查询时会双向检查
- 删除好友关系会完全移除数据库记录
- 拒绝的好友请求会保留在数据库中，状态为 "rejected"
