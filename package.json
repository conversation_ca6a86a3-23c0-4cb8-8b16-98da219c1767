{"name": "carboncoin-backend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@prisma/client": "^6.15.0", "bcryptjs": "^3.0.2", "next": "15.5.2", "prisma": "^6.15.0", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "typescript": "^5"}}