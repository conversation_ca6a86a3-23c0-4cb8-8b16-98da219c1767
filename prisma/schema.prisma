generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model UserLocation {
  id              String   @id @default(cuid())
  userId          String
  lastLocation    Json
  lastUpdate      DateTime @db.DateTime(0)
  lastOnlineTime  DateTime @db.DateTime(0)
  recentLocations Json?
  user            User     @relation(fields: [userId], references: [userId])

  @@index([userId], map: "UserLocation_userId_fkey")
}

model User {
  id             String         @id @default(cuid())
  userId         String         @unique
  passwordHash   String?
  nickname       String
  avatar         String?
  lastActiveTime DateTime?      @db.DateTime(0)
  createdAt      DateTime?      @db.DateTime(0)
  friendships    Friendship[]   @relation("UserFriendships")
  locations      UserLocation[]
}

model Friendship {
  id        String           @id @default(cuid())
  userId    String
  friendId  String
  status    FriendshipStatus
  createdAt DateTime         @db.DateTime(0)
  updatedAt DateTime         @db.DateTime(0)
  user      User             @relation("UserFriendships", fields: [userId], references: [userId])

  @@index([userId], map: "Friendship_userId_fkey")
}

enum FriendshipStatus {
  pending
  accepted
  rejected
}
